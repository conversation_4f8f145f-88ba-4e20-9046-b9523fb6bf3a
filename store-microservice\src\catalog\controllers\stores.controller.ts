import {
  Controller,
  Get,
  Param,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { CatalogProductsService } from '../services/catalog-products.service';
import { QueryCatalogDto } from '../dto/query-catalog.dto';
import { CatalogProductDto } from '../dto/catalog-product.dto';

@ApiTags('catalog-stores')
@Controller('api/catalog/stores')
export class StoresController {
  constructor(private readonly catalogProductsService: CatalogProductsService) {}

  @Get(':id/products')
  @ApiOperation({ summary: 'Obtener productos de una tienda específica' })
  @ApiParam({ name: 'id', description: 'ID de la tienda' })
  @ApiQuery({ name: 'page', required: false, description: 'Número de página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Elementos por página' })
  @ApiResponse({ 
    status: 200, 
    description: 'Lista de productos de la tienda obtenida exitosamente',
    type: [CatalogProductDto],
  })
  getStoreProducts(
    @Param('id') storeId: string,
    @Query() queryDto: QueryCatalogDto,
  ) {
    return this.catalogProductsService.findByStore(storeId, queryDto);
  }
}
