import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNumber,
  IsArray,
  ValidateNested,
  IsEnum,
  IsPositive,
  IsEmail,
  IsOptional,
} from 'class-validator';
import { DeliveryMethod } from '../../orders/schemas/order.schema';

class CatalogCustomerDto {
  @ApiProperty({ description: 'Nombre del cliente' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Dirección del cliente' })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({ description: 'Teléfono del cliente' })
  @IsString()
  phone: string;

  @ApiPropertyOptional({ description: 'Email del cliente' })
  @IsOptional()
  @IsEmail()
  email?: string;
}

class CatalogOrderItemDto {
  @ApiProperty({ description: 'ID del producto' })
  @IsString()
  productId: string;

  @ApiProperty({ description: 'Cantidad', minimum: 1 })
  @IsNumber()
  @IsPositive()
  quantity: number;

  @ApiProperty({ description: 'Método de entrega para este item' })
  @IsEnum(DeliveryMethod)
  deliveryMethod: DeliveryMethod;
}

export class CreateCatalogOrderDto {
  @ApiProperty({ description: 'Información del cliente' })
  @ValidateNested()
  @Type(() => CatalogCustomerDto)
  customer: CatalogCustomerDto;

  @ApiProperty({ 
    description: 'Items del pedido',
    type: [CatalogOrderItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CatalogOrderItemDto)
  items: CatalogOrderItemDto[];

  @ApiPropertyOptional({ description: 'Notas adicionales' })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({ description: 'Método de pago' })
  @IsOptional()
  @IsString()
  paymentMethod?: string;
}

export class CatalogOrderResponseDto {
  @ApiProperty({ description: 'ID del pedido creado' })
  orderId: string;

  @ApiProperty({ description: 'Número de seguimiento' })
  trackingNumber: string;

  @ApiProperty({ description: 'Total del pedido' })
  total: number;

  @ApiProperty({ description: 'Estado del pedido' })
  status: string;

  @ApiProperty({ description: 'Fecha estimada de entrega' })
  estimatedDelivery: string;
}
