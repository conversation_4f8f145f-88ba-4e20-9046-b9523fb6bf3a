import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Product, ProductDocument } from '../../products/schemas/product.schema';
import { Review, ReviewDocument } from '../../reviews/schemas/review.schema';
import { QueryCatalogDto } from '../dto/query-catalog.dto';
import { CatalogProductDto } from '../dto/catalog-product.dto';
import { CategoryDto } from '../dto/category.dto';
import { PaginationResponseDto } from '../../common/dto/pagination.dto';

@Injectable()
export class CatalogProductsService {
  constructor(
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
    @InjectModel(Review.name) private reviewModel: Model<ReviewDocument>,
  ) {}

  async findAll(queryDto: QueryCatalogDto): Promise<{
    data: CatalogProductDto[];
    pagination: PaginationResponseDto;
  }> {
    const { page = 1, limit = 10, search, category, storeId } = queryDto;
    const skip = (page - 1) * limit;

    // Build filter - solo productos activos
    const filter: any = { isActive: true };
    
    if (search) {
      filter.name = { $regex: search, $options: 'i' };
    }
    
    if (category) {
      filter.category = category;
    }

    if (storeId) {
      filter.storeId = storeId;
    }

    // Execute queries
    const [products, total] = await Promise.all([
      this.productModel
        .find(filter)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.productModel.countDocuments(filter).exec(),
    ]);

    // Transform to catalog format with ratings
    const catalogProducts = await Promise.all(
      products.map(async (product) => {
        const rating = await this.getProductRating(product._id.toString());
        return this.transformToCatalogProduct(product, rating);
      })
    );

    const totalPages = Math.ceil(total / limit);

    return {
      data: catalogProducts,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  async findOne(id: string): Promise<CatalogProductDto> {
    const product = await this.productModel.findOne({ 
      _id: id, 
      isActive: true 
    }).exec();
    
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found or not available`);
    }

    const rating = await this.getProductRating(id);
    return this.transformToCatalogProduct(product, rating);
  }

  async findByStore(storeId: string, queryDto: QueryCatalogDto): Promise<{
    data: CatalogProductDto[];
    pagination: PaginationResponseDto;
  }> {
    return this.findAll({ ...queryDto, storeId });
  }

  async getCategories(): Promise<CategoryDto[]> {
    const categories = await this.productModel.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $match: { _id: { $ne: null } } },
      { $sort: { count: -1 } },
    ]);

    return categories.map(cat => ({
      name: cat._id,
      count: cat.count,
    }));
  }

  private async getProductRating(productId: string): Promise<number> {
    const result = await this.reviewModel.aggregate([
      { $match: { productId } },
      { $group: { _id: null, averageRating: { $avg: '$rating' } } },
    ]);
    
    return result.length > 0 ? Math.round(result[0].averageRating * 10) / 10 : 0;
  }

  private transformToCatalogProduct(product: ProductDocument, rating: number): CatalogProductDto {
    return {
      id: product._id.toString(),
      name: product.name,
      description: product.description,
      price: product.price,
      stock: product.stock,
      category: product.category,
      image: product.image,
      tags: product.tags,
      storeId: 'store-001', // TODO: Implementar sistema de tiendas
      storeName: 'Tienda Principal', // TODO: Implementar sistema de tiendas
      rating,
      deliveryOptions: product.deliveryOptions,
      nutritionalInfo: product.nutritionalInfo,
    };
  }
}
