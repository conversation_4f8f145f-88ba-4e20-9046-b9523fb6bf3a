import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CatalogProductDto {
  @ApiProperty({ description: 'ID del producto' })
  id: string;

  @ApiProperty({ description: 'Nombre del producto' })
  name: string;

  @ApiPropertyOptional({ description: 'Descripción del producto' })
  description?: string;

  @ApiProperty({ description: 'Precio del producto' })
  price: number;

  @ApiProperty({ description: 'Stock disponible' })
  stock: number;

  @ApiPropertyOptional({ description: 'Categoría del producto' })
  category?: string;

  @ApiPropertyOptional({ description: 'URL de la imagen del producto' })
  image?: string;

  @ApiPropertyOptional({ description: 'Etiquetas del producto', type: [String] })
  tags?: string[];

  @ApiProperty({ description: 'ID de la tienda' })
  storeId: string;

  @ApiProperty({ description: 'Nombre de la tienda' })
  storeName: string;

  @ApiProperty({ description: 'Calificación promedio' })
  rating: number;

  @ApiPropertyOptional({ description: 'Opciones de entrega' })
  deliveryOptions?: {
    delivery: boolean;
    pickup: boolean;
  };

  @ApiPropertyOptional({ description: 'Información nutricional' })
  nutritionalInfo?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
}
