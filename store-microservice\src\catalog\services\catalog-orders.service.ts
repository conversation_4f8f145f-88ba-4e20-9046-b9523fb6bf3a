import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Order, OrderDocument, OrderStatus } from '../../orders/schemas/order.schema';
import { Product, ProductDocument } from '../../products/schemas/product.schema';
import { CreateCatalogOrderDto, CatalogOrderResponseDto } from '../dto/catalog-order.dto';
import { CartService } from './cart.service';

@Injectable()
export class CatalogOrdersService {
  constructor(
    @InjectModel(Order.name) private orderModel: Model<OrderDocument>,
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
    private cartService: CartService,
  ) {}

  async createOrder(
    sessionId: string,
    createOrderDto: CreateCatalogOrderDto
  ): Promise<CatalogOrderResponseDto> {
    const { customer, items, notes, paymentMethod } = createOrderDto;

    // Validar items y calcular total
    let total = 0;
    const orderItems = [];

    for (const item of items) {
      const product = await this.productModel.findOne({ 
        _id: item.productId, 
        isActive: true 
      }).exec();

      if (!product) {
        throw new NotFoundException(`Product ${item.productId} not found or not available`);
      }

      if (product.stock < item.quantity) {
        throw new BadRequestException(
          `Insufficient stock for ${product.name}. Available: ${product.stock}`
        );
      }

      const itemTotal = product.price * item.quantity;
      total += itemTotal;

      orderItems.push({
        id: product._id.toString(),
        name: product.name,
        quantity: item.quantity,
        price: product.price,
      });

      // Actualizar stock del producto
      await this.productModel.findByIdAndUpdate(
        item.productId,
        { $inc: { stock: -item.quantity } }
      ).exec();
    }

    // Calcular impuestos y envío
    const taxes = Math.round(total * 0.1 * 100) / 100;
    const shipping = total >= 50 ? 0 : 5;
    const finalTotal = total + taxes + shipping;

    // Crear el pedido
    const order = new this.orderModel({
      customer,
      date: new Date().toISOString(),
      total: finalTotal,
      status: OrderStatus.PENDING,
      deliveryMethod: items[0].deliveryMethod, // Usar el método del primer item
      items: orderItems,
      notes,
    });

    const savedOrder = await order.save();

    // Limpiar el carrito después de crear el pedido
    await this.cartService.clearCart(sessionId);

    // Generar número de seguimiento
    const trackingNumber = this.generateTrackingNumber(savedOrder._id.toString());

    // Calcular fecha estimada de entrega (ejemplo: 3-5 días)
    const estimatedDelivery = new Date();
    estimatedDelivery.setDate(estimatedDelivery.getDate() + 4);

    return {
      orderId: savedOrder._id.toString(),
      trackingNumber,
      total: finalTotal,
      status: savedOrder.status,
      estimatedDelivery: estimatedDelivery.toISOString().split('T')[0],
    };
  }

  async getOrderStatus(orderId: string): Promise<{
    orderId: string;
    status: OrderStatus;
    trackingNumber: string;
    estimatedDelivery: string;
  }> {
    const order = await this.orderModel.findById(orderId).exec();
    
    if (!order) {
      throw new NotFoundException('Order not found');
    }

    const trackingNumber = this.generateTrackingNumber(orderId);
    const estimatedDelivery = new Date(order.date);
    estimatedDelivery.setDate(estimatedDelivery.getDate() + 4);

    return {
      orderId,
      status: order.status,
      trackingNumber,
      estimatedDelivery: estimatedDelivery.toISOString().split('T')[0],
    };
  }

  private generateTrackingNumber(orderId: string): string {
    // Generar número de seguimiento basado en el ID del pedido
    const prefix = 'TRK';
    const timestamp = Date.now().toString().slice(-6);
    const orderSuffix = orderId.slice(-4).toUpperCase();
    return `${prefix}${timestamp}${orderSuffix}`;
  }
}
