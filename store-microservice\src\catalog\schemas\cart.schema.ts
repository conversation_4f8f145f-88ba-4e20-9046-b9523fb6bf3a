import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { DeliveryMethod } from '../../orders/schemas/order.schema';

export type CartDocument = Cart & Document;

@Schema({ timestamps: true })
export class CartItem {
  @Prop({ required: true })
  productId: string;

  @Prop({ required: true })
  productName: string;

  @Prop({ required: true })
  price: number;

  @Prop({ required: true })
  quantity: number;

  @Prop({ required: true })
  subtotal: number;

  @Prop({ 
    type: String, 
    enum: DeliveryMethod, 
    required: true 
  })
  deliveryMethod: DeliveryMethod;

  @Prop()
  image?: string;

  @Prop({ required: true })
  storeId: string;

  @Prop({ required: true })
  storeName: string;
}

@Schema({ timestamps: true })
export class Cart {
  @Prop({ required: true, unique: true })
  sessionId: string;

  @Prop({
    type: [CartItem],
    default: [],
  })
  items: CartItem[];

  @Prop({ default: 0 })
  totalItems: number;

  @Prop({ default: 0 })
  subtotal: number;

  @Prop({ default: 0 })
  taxes: number;

  @Prop({ default: 0 })
  shipping: number;

  @Prop({ default: 0 })
  total: number;

  @Prop({ default: Date.now, expires: 86400 }) // Expira en 24 horas
  expiresAt: Date;
}

export const CartSchema = SchemaFactory.createForClass(Cart);
export const CartItemSchema = SchemaFactory.createForClass(CartItem);
