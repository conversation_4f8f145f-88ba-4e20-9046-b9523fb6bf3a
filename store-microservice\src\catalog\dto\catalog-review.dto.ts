import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, Min, Max } from 'class-validator';

export class CreateCatalogReviewDto {
  @ApiProperty({ description: 'ID del producto' })
  @IsString()
  productId: string;

  @ApiProperty({ description: 'Calificación del 1 al 5', minimum: 1, maximum: 5 })
  @IsNumber()
  @Min(1)
  @Max(5)
  rating: number;

  @ApiProperty({ description: 'Comentario de la reseña' })
  @IsString()
  comment: string;

  @ApiProperty({ description: 'Nombre del cliente' })
  @IsString()
  customerName: string;
}

export class CatalogReviewDto {
  @ApiProperty({ description: 'ID de la reseña' })
  id: string;

  @ApiProperty({ description: 'Nombre del cliente' })
  customerName: string;

  @ApiProperty({ description: 'Calificación' })
  rating: number;

  @ApiProperty({ description: 'Comentario' })
  comment: string;

  @ApiPropertyOptional({ description: 'Respuesta de la tienda' })
  reply?: string;

  @ApiProperty({ description: 'Fecha de la reseña' })
  reviewDate: Date;

  @ApiPropertyOptional({ description: 'Fecha de la respuesta' })
  replyDate?: Date;
}
