import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Cart, CartDocument } from '../schemas/cart.schema';
import { Product, ProductDocument } from '../../products/schemas/product.schema';
import { AddToCartDto, CartDto } from '../dto/cart.dto';

@Injectable()
export class CartService {
  constructor(
    @InjectModel(Cart.name) private cartModel: Model<CartDocument>,
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
  ) {}

  async addToCart(sessionId: string, addToCartDto: AddToCartDto): Promise<CartDto> {
    const { productId, quantity, deliveryMethod } = addToCartDto;

    // Verificar que el producto existe y está activo
    const product = await this.productModel.findOne({ 
      _id: productId, 
      isActive: true 
    }).exec();

    if (!product) {
      throw new NotFoundException('Product not found or not available');
    }

    // Verificar stock disponible
    if (product.stock < quantity) {
      throw new BadRequestException(`Insufficient stock. Available: ${product.stock}`);
    }

    // Buscar o crear carrito
    let cart = await this.cartModel.findOne({ sessionId }).exec();
    if (!cart) {
      cart = new this.cartModel({ sessionId, items: [] });
    }

    // Verificar si el producto ya está en el carrito con el mismo método de entrega
    const existingItemIndex = cart.items.findIndex(
      item => item.productId === productId && item.deliveryMethod === deliveryMethod
    );

    if (existingItemIndex >= 0) {
      // Actualizar cantidad del item existente
      const newQuantity = cart.items[existingItemIndex].quantity + quantity;
      
      if (product.stock < newQuantity) {
        throw new BadRequestException(`Insufficient stock. Available: ${product.stock}`);
      }

      cart.items[existingItemIndex].quantity = newQuantity;
      cart.items[existingItemIndex].subtotal = newQuantity * product.price;
    } else {
      // Agregar nuevo item al carrito
      const newItem = {
        productId: product._id.toString(),
        productName: product.name,
        price: product.price,
        quantity,
        subtotal: quantity * product.price,
        deliveryMethod,
        image: product.image,
        storeId: 'store-001', // TODO: Implementar sistema de tiendas
        storeName: 'Tienda Principal', // TODO: Implementar sistema de tiendas
      };

      cart.items.push(newItem as any);
    }

    // Recalcular totales
    this.recalculateCart(cart);

    // Guardar carrito
    await cart.save();

    return this.transformToCartDto(cart);
  }

  async getCart(sessionId: string): Promise<CartDto> {
    const cart = await this.cartModel.findOne({ sessionId }).exec();
    
    if (!cart) {
      // Retornar carrito vacío
      return {
        items: [],
        totalItems: 0,
        subtotal: 0,
        taxes: 0,
        shipping: 0,
        total: 0,
      };
    }

    return this.transformToCartDto(cart);
  }

  async removeFromCart(sessionId: string, itemId: string): Promise<CartDto> {
    const cart = await this.cartModel.findOne({ sessionId }).exec();
    
    if (!cart) {
      throw new NotFoundException('Cart not found');
    }

    // Filtrar el item a eliminar
    const initialLength = cart.items.length;
    cart.items = cart.items.filter(item => item._id?.toString() !== itemId);

    if (cart.items.length === initialLength) {
      throw new NotFoundException('Item not found in cart');
    }

    // Recalcular totales
    this.recalculateCart(cart);

    // Guardar carrito
    await cart.save();

    return this.transformToCartDto(cart);
  }

  async clearCart(sessionId: string): Promise<void> {
    await this.cartModel.findOneAndDelete({ sessionId }).exec();
  }

  private recalculateCart(cart: CartDocument): void {
    cart.totalItems = cart.items.reduce((total, item) => total + item.quantity, 0);
    cart.subtotal = cart.items.reduce((total, item) => total + item.subtotal, 0);
    
    // Calcular impuestos (ejemplo: 10%)
    cart.taxes = Math.round(cart.subtotal * 0.1 * 100) / 100;
    
    // Calcular envío (ejemplo: gratis si es mayor a $50, sino $5)
    cart.shipping = cart.subtotal >= 50 ? 0 : 5;
    
    cart.total = cart.subtotal + cart.taxes + cart.shipping;
  }

  private transformToCartDto(cart: CartDocument): CartDto {
    return {
      items: cart.items.map(item => ({
        id: item._id?.toString() || '',
        productId: item.productId,
        productName: item.productName,
        price: item.price,
        quantity: item.quantity,
        subtotal: item.subtotal,
        deliveryMethod: item.deliveryMethod,
        image: item.image,
        storeId: item.storeId,
        storeName: item.storeName,
      })),
      totalItems: cart.totalItems,
      subtotal: cart.subtotal,
      taxes: cart.taxes,
      shipping: cart.shipping,
      total: cart.total,
    };
  }
}
