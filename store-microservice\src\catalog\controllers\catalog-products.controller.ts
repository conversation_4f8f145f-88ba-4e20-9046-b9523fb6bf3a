import {
  Controller,
  Get,
  Param,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { CatalogProductsService } from '../services/catalog-products.service';
import { QueryCatalogDto } from '../dto/query-catalog.dto';
import { CatalogProductDto } from '../dto/catalog-product.dto';
import { CategoryDto } from '../dto/category.dto';

@ApiTags('catalog-products')
@Controller('api/catalog/products')
export class CatalogProductsController {
  constructor(private readonly catalogProductsService: CatalogProductsService) {}

  @Get()
  @ApiOperation({ summary: 'Listar productos del catálogo público con paginación' })
  @ApiResponse({ 
    status: 200, 
    description: 'Lista de productos obtenida exitosamente',
    type: [CatalogProductDto],
  })
  @ApiQuery({ name: 'page', required: false, description: 'Número de página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Elementos por página' })
  @ApiQuery({ name: 'search', required: false, description: 'Buscar por nombre' })
  @ApiQuery({ name: 'category', required: false, description: 'Filtrar por categoría' })
  findAll(@Query() queryDto: QueryCatalogDto) {
    return this.catalogProductsService.findAll(queryDto);
  }

  @Get('categories')
  @ApiOperation({ summary: 'Obtener categorías disponibles' })
  @ApiResponse({ 
    status: 200, 
    description: 'Lista de categorías obtenida exitosamente',
    type: [CategoryDto],
  })
  getCategories(): Promise<CategoryDto[]> {
    return this.catalogProductsService.getCategories();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obtener detalles de un producto específico del catálogo' })
  @ApiParam({ name: 'id', description: 'ID del producto' })
  @ApiResponse({ 
    status: 200, 
    description: 'Producto encontrado',
    type: CatalogProductDto,
  })
  @ApiResponse({ status: 404, description: 'Producto no encontrado o no disponible' })
  findOne(@Param('id') id: string): Promise<CatalogProductDto> {
    return this.catalogProductsService.findOne(id);
  }
}
