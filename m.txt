# MICROSERVICIO STORE - DOCUMENTACIÓN PARA FRONTEND

## INFORMACIÓN GENERAL
- **Puerto**: 3002
- **Base URL**: http://localhost:3002/api/store
- **Documentación Swagger**: http://localhost:3002/api/docs
- **Base de datos**: MongoDB (puerto 27017)

## ESTRUCTURA DE RESPUESTAS
Todas las respuestas exitosas tienen el siguiente formato:
```json
{
  "success": true,
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

Las respuestas de error tienen eel formato:
```json
{
  "statusCode": 400,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/store/products",
  "method": "POST",
  "message": "Descripción del error"
}
```

## ENDPOINTS DISPONIBLES

### 1. DASHBOARD
**GET /api/store/dashboard**
- **Propósito**: Obtener estadísticas generales de la tienda
- **Parámetros**: Ninguno
- **Respuesta esperada**:
```json
{
  "totalProducts": 150,
  "pendingOrders": 25,
  "completedOrders": 340,
  "totalRevenue": 15750.50,
  "averageRating": 4.2
}
```
- **Uso en frontend**: Para mostrar métricas en el dashboard principal

### 2. PRODUCTOS

#### GET /api/store/products
- **Propósito**: Listar productos con paginación y filtros
- **Parámetros de consulta**:
  - `page` (opcional): Número de página (default: 1)
  - `limit` (opcional): Elementos por página (default: 10, máx: 100)
  - `search` (opcional): Buscar por nombre del producto
  - `category` (opcional): Filtrar por categoría
  - `isActive` (opcional): Filtrar por productos activos (true/false)
- **Ejemplo**: `/api/store/products?page=1&limit=10&search=pizza&isActive=true`
- **Respuesta**:
```json
{
  "data": [
    {
      "_id": "507f1f77bcf86cd799439011",
      "name": "Pizza Margherita",
      "description": "Pizza clásica con tomate y mozzarella",
      "price": 12.99,
      "stock": 50,
      "category": "Pizzas",
      "image": "https://example.com/pizza.jpg",
      "tags": ["vegetariana", "clásica"],
      "deliveryOptions": {
        "delivery": true,
        "pickup": true
      },
      "nutritionalInfo": {
        "calories": 250,
        "protein": 12,
        "carbs": 30,
        "fat": 8
      },
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 150,
    "totalPages": 15,
    "hasNext": true,
    "hasPrev": false
  }
}
```

#### GET /api/store/products/:id
- **Propósito**: Obtener detalles de un producto específico
- **Parámetros**: `id` del producto en la URL
- **Respuesta**: Objeto del producto (mismo formato que en la lista)

#### POST /api/store/products
- **Propósito**: Crear un nuevo producto
- **Body requerido**:
```json
{
  "name": "Pizza Margherita",
  "description": "Pizza clásica con tomate y mozzarella",
  "price": 12.99,
  "stock": 50,
  "category": "Pizzas",
  "image": "https://example.com/pizza.jpg",
  "tags": ["vegetariana", "clásica"],
  "deliveryOptions": {
    "delivery": true,
    "pickup": true
  },
  "nutritionalInfo": {
    "calories": 250,
    "protein": 12,
    "carbs": 30,
    "fat": 8
  },
  "isActive": true
}
```
- **Campos obligatorios**: `name`, `price`
- **Respuesta**: Producto creado con ID asignado

#### PATCH /api/store/products/:id
- **Propósito**: Actualizar un producto existente
- **Body**: Cualquier campo del producto (parcial)
- **Respuesta**: Producto actualizado

#### DELETE /api/store/products/:id
- **Propósito**: Eliminar un producto
- **Respuesta**: 204 No Content (sin body)

### 3. PEDIDOS

#### GET /api/store/orders
- **Propósito**: Listar pedidos con paginación y filtros
- **Parámetros de consulta**:
  - `page`, `limit`: Paginación
  - `status`: Filtrar por estado (pending, processing, ready, delivered, cancelled)
- **Respuesta**:
```json
{
  "data": [
    {
      "_id": "507f1f77bcf86cd799439012",
      "customer": {
        "name": "Juan Pérez",
        "address": "Calle 123, Ciudad",
        "phone": "+1234567890",
        "email": "<EMAIL>"
      },
      "date": "2024-01-01T12:00:00.000Z",
      "total": 25.98,
      "status": "pending",
      "deliveryMethod": "delivery",
      "items": [
        {
          "id": "507f1f77bcf86cd799439011",
          "name": "Pizza Margherita",
          "quantity": 2,
          "price": 12.99
        }
      ],
      "notes": "Sin cebolla por favor",
      "createdAt": "2024-01-01T12:00:00.000Z"
    }
  ],
  "pagination": { ... }
}
```

#### GET /api/store/orders/:id
- **Propósito**: Obtener detalles de un pedido específico
- **Respuesta**: Objeto del pedido completo

#### POST /api/store/orders
- **Propósito**: Crear un nuevo pedido
- **Body requerido**:
```json
{
  "customer": {
    "name": "Juan Pérez",
    "address": "Calle 123, Ciudad",
    "phone": "+1234567890",
    "email": "<EMAIL>"
  },
  "date": "2024-01-01T12:00:00.000Z",
  "total": 25.98,
  "deliveryMethod": "delivery",
  "items": [
    {
      "id": "507f1f77bcf86cd799439011",
      "name": "Pizza Margherita",
      "quantity": 2,
      "price": 12.99
    }
  ],
  "notes": "Sin cebolla por favor"
}
```
- **Campos obligatorios**: `customer`, `date`, `total`, `deliveryMethod`, `items`

#### PATCH /api/store/orders/:id/status
- **Propósito**: Actualizar solo el estado de un pedido
- **Body**:
```json
{
  "status": "processing"
}
```
- **Estados válidos**: pending, processing, ready, delivered, cancelled

### 4. PROVEEDORES

#### GET /api/store/suppliers
- **Propósito**: Listar proveedores con paginación
- **Parámetros**: `page`, `limit`, `search` (buscar por nombre)
- **Respuesta**:
```json
{
  "data": [
    {
      "_id": "507f1f77bcf86cd799439013",
      "name": "Distribuidora ABC",
      "contactPerson": "María García",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "address": "Av. Principal 456",
      "products": ["Harina", "Tomate", "Queso"],
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pagination": { ... }
}
```

#### GET /api/store/suppliers/:id
- **Propósito**: Obtener detalles de un proveedor

#### POST /api/store/suppliers
- **Propósito**: Crear un nuevo proveedor
- **Body requerido**:
```json
{
  "name": "Distribuidora ABC",
  "contactPerson": "María García",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "address": "Av. Principal 456",
  "products": ["Harina", "Tomate", "Queso"]
}
```
- **Campos obligatorios**: `name`, `contactPerson`, `email`, `phone`, `address`

#### PATCH /api/store/suppliers/:id
- **Propósito**: Actualizar un proveedor

#### DELETE /api/store/suppliers/:id
- **Propósito**: Eliminar un proveedor

### 5. RESEÑAS

#### GET /api/store/reviews
- **Propósito**: Listar reseñas con paginación
- **Parámetros**: `page`, `limit`, `productId` (filtrar por producto)
- **Respuesta**:
```json
{
  "data": [
    {
      "_id": "507f1f77bcf86cd799439014",
      "customerName": "Ana López",
      "productId": "507f1f77bcf86cd799439011",
      "productName": "Pizza Margherita",
      "rating": 5,
      "comment": "Excelente pizza, muy recomendada",
      "reply": "Gracias por tu comentario",
      "reviewDate": "2024-01-01T00:00:00.000Z",
      "replyDate": "2024-01-02T00:00:00.000Z"
    }
  ],
  "pagination": { ... }
}
```

#### GET /api/store/reviews/:id
- **Propósito**: Obtener detalles de una reseña específica

#### PATCH /api/store/reviews/:id/reply
- **Propósito**: Responder a una reseña
- **Body**:
```json
{
  "reply": "Gracias por tu comentario, nos alegra que te haya gustado"
}
```

## CÓDIGOS DE ESTADO HTTP
- **200**: Operación exitosa
- **201**: Recurso creado exitosamente
- **204**: Operación exitosa sin contenido (DELETE)
- **400**: Datos inválidos o faltantes
- **404**: Recurso no encontrado
- **500**: Error interno del servidor

## VALIDACIONES
- Todos los endpoints validan automáticamente los datos de entrada
- Los campos requeridos deben estar presentes
- Los tipos de datos deben coincidir con lo especificado
- Los emails deben tener formato válido
- Los números deben ser positivos donde corresponda
- Las fechas deben estar en formato ISO string

## PAGINACIÓN
- Todas las listas soportan paginación
- Parámetros: `page` (número de página) y `limit` (elementos por página)
- La respuesta incluye información de paginación completa
- Límite máximo: 100 elementos por página

## FILTROS Y BÚSQUEDA
- **Productos**: Buscar por nombre, filtrar por categoría y estado activo
- **Pedidos**: Filtrar por estado
- **Proveedores**: Buscar por nombre
- **Reseñas**: Filtrar por producto

## NOTAS IMPORTANTES
1. Todos los IDs son ObjectIds de MongoDB (24 caracteres hexadecimales)
2. Las fechas se manejan en formato ISO string
3. Los precios son números decimales
4. Las respuestas están envueltas en un objeto con metadata
5. Los errores incluyen información detallada para debugging
6. La documentación completa está disponible en Swagger UI
