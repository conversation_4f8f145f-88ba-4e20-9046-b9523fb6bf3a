import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Review, ReviewDocument } from '../../reviews/schemas/review.schema';
import { Product, ProductDocument } from '../../products/schemas/product.schema';
import { CreateCatalogReviewDto, CatalogReviewDto } from '../dto/catalog-review.dto';
import { PaginationDto, PaginationResponseDto } from '../../common/dto/pagination.dto';

@Injectable()
export class CatalogReviewsService {
  constructor(
    @InjectModel(Review.name) private reviewModel: Model<ReviewDocument>,
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
  ) {}

  async getProductReviews(
    productId: string, 
    paginationDto: PaginationDto
  ): Promise<{
    data: CatalogReviewDto[];
    pagination: PaginationResponseDto;
  }> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    // Verificar que el producto existe
    const product = await this.productModel.findOne({ 
      _id: productId, 
      isActive: true 
    }).exec();

    if (!product) {
      throw new NotFoundException('Product not found or not available');
    }

    // Obtener reseñas del producto
    const [reviews, total] = await Promise.all([
      this.reviewModel
        .find({ productId })
        .skip(skip)
        .limit(limit)
        .sort({ reviewDate: -1 })
        .exec(),
      this.reviewModel.countDocuments({ productId }).exec(),
    ]);

    const catalogReviews = reviews.map(review => this.transformToCatalogReview(review));

    const totalPages = Math.ceil(total / limit);

    return {
      data: catalogReviews,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  async createReview(createReviewDto: CreateCatalogReviewDto): Promise<CatalogReviewDto> {
    const { productId, rating, comment, customerName } = createReviewDto;

    // Verificar que el producto existe y está activo
    const product = await this.productModel.findOne({ 
      _id: productId, 
      isActive: true 
    }).exec();

    if (!product) {
      throw new NotFoundException('Product not found or not available');
    }

    // Crear la reseña
    const review = new this.reviewModel({
      customerName,
      productId,
      productName: product.name,
      rating,
      comment,
      reviewDate: new Date(),
    });

    const savedReview = await review.save();
    return this.transformToCatalogReview(savedReview);
  }

  async getReviewById(reviewId: string): Promise<CatalogReviewDto> {
    const review = await this.reviewModel.findById(reviewId).exec();
    
    if (!review) {
      throw new NotFoundException('Review not found');
    }

    return this.transformToCatalogReview(review);
  }

  private transformToCatalogReview(review: ReviewDocument): CatalogReviewDto {
    return {
      id: review._id.toString(),
      customerName: review.customerName,
      rating: review.rating,
      comment: review.comment,
      reply: review.reply,
      reviewDate: review.reviewDate,
      replyDate: review.replyDate,
    };
  }
}
