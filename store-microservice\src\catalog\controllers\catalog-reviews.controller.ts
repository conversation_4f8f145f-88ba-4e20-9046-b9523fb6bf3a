import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { CatalogReviewsService } from '../services/catalog-reviews.service';
import { CreateCatalogReviewDto, CatalogReviewDto } from '../dto/catalog-review.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';

@ApiTags('catalog-reviews')
@Controller('api/catalog/reviews')
export class CatalogReviewsController {
  constructor(private readonly catalogReviewsService: CatalogReviewsService) {}

  @Get('product/:id')
  @ApiOperation({ summary: 'Obtener reseñas de un producto' })
  @ApiParam({ name: 'id', description: 'ID del producto' })
  @ApiQuery({ name: 'page', required: false, description: 'Número de página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Elementos por página' })
  @ApiResponse({ 
    status: 200, 
    description: 'Lista de reseñas obtenida exitosamente',
    type: [CatalogReviewDto],
  })
  @ApiResponse({ status: 404, description: 'Producto no encontrado' })
  getProductReviews(
    @Param('id') productId: string,
    @Query() paginationDto: PaginationDto,
  ) {
    return this.catalogReviewsService.getProductReviews(productId, paginationDto);
  }

  @Post()
  @ApiOperation({ summary: 'Crear una nueva reseña' })
  @ApiResponse({ 
    status: 201, 
    description: 'Reseña creada exitosamente',
    type: CatalogReviewDto,
  })
  @ApiResponse({ status: 400, description: 'Datos inválidos' })
  @ApiResponse({ status: 404, description: 'Producto no encontrado' })
  createReview(@Body() createReviewDto: CreateCatalogReviewDto): Promise<CatalogReviewDto> {
    return this.catalogReviewsService.createReview(createReviewDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obtener detalles de una reseña específica' })
  @ApiParam({ name: 'id', description: 'ID de la reseña' })
  @ApiResponse({ 
    status: 200, 
    description: 'Reseña encontrada',
    type: CatalogReviewDto,
  })
  @ApiResponse({ status: 404, description: 'Reseña no encontrada' })
  getReview(@Param('id') reviewId: string): Promise<CatalogReviewDto> {
    return this.catalogReviewsService.getReviewById(reviewId);
  }
}
