import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsEnum, IsPositive, Min } from 'class-validator';
import { DeliveryMethod } from '../../orders/schemas/order.schema';

export class AddToCartDto {
  @ApiProperty({ description: 'ID del producto' })
  @IsString()
  productId: string;

  @ApiProperty({ description: 'Cantidad', minimum: 1 })
  @IsNumber()
  @IsPositive()
  @Min(1)
  quantity: number;

  @ApiProperty({ 
    description: 'Método de entrega',
    enum: DeliveryMethod,
  })
  @IsEnum(DeliveryMethod)
  deliveryMethod: DeliveryMethod;
}

export class CartItemDto {
  @ApiProperty({ description: 'ID del item en el carrito' })
  id: string;

  @ApiProperty({ description: 'ID del producto' })
  productId: string;

  @ApiProperty({ description: 'Nombre del producto' })
  productName: string;

  @ApiProperty({ description: 'Precio unitario' })
  price: number;

  @ApiProperty({ description: 'Cantidad' })
  quantity: number;

  @ApiProperty({ description: 'Subtotal' })
  subtotal: number;

  @ApiProperty({ description: 'Método de entrega' })
  deliveryMethod: DeliveryMethod;

  @ApiPropertyOptional({ description: 'URL de la imagen del producto' })
  image?: string;

  @ApiProperty({ description: 'ID de la tienda' })
  storeId: string;

  @ApiProperty({ description: 'Nombre de la tienda' })
  storeName: string;
}

export class CartDto {
  @ApiProperty({ description: 'Items en el carrito', type: [CartItemDto] })
  items: CartItemDto[];

  @ApiProperty({ description: 'Total de items' })
  totalItems: number;

  @ApiProperty({ description: 'Subtotal' })
  subtotal: number;

  @ApiProperty({ description: 'Impuestos' })
  taxes: number;

  @ApiProperty({ description: 'Costo de envío' })
  shipping: number;

  @ApiProperty({ description: 'Total final' })
  total: number;
}
